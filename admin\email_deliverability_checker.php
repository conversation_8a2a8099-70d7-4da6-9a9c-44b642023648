<?php
/**
 * Email Deliverability Checker
 * Checks DNS records and email configuration for spam prevention
 */

require_once '../includes/auth.php';
require_once '../includes/header.php';

// Function to check DNS records
function check_dns_record($domain, $type, $expected_contains = null) {
    $records = dns_get_record($domain, $type);
    $found = false;
    $record_value = '';
    
    if ($records) {
        foreach ($records as $record) {
            if ($type === DNS_TXT && isset($record['txt'])) {
                $record_value = $record['txt'];
                if ($expected_contains && strpos($record_value, $expected_contains) !== false) {
                    $found = true;
                    break;
                }
            } elseif ($type === DNS_MX && isset($record['target'])) {
                $record_value = $record['target'];
                $found = true;
            }
        }
    }
    
    return ['found' => $found, 'value' => $record_value, 'all_records' => $records];
}

// Check domain configuration
$domain = 'elsbeedata.site';
$checks = [];

// SPF Check
$spf_check = check_dns_record($domain, DNS_TXT, 'v=spf1');
$checks['spf'] = [
    'name' => 'SPF Record',
    'status' => $spf_check['found'],
    'value' => $spf_check['value'],
    'description' => 'Authorizes servers to send email for your domain',
    'fix' => 'Add TXT record: v=spf1 a mx include:_spf.google.com ~all'
];

// DKIM Check
$dkim_check = check_dns_record('default._domainkey.' . $domain, DNS_TXT, 'v=DKIM1');
$checks['dkim'] = [
    'name' => 'DKIM Record',
    'status' => $dkim_check['found'],
    'value' => $dkim_check['value'],
    'description' => 'Digitally signs emails to prove authenticity',
    'fix' => 'Contact hosting provider to set up DKIM or use Gmail SMTP'
];

// DMARC Check
$dmarc_check = check_dns_record('_dmarc.' . $domain, DNS_TXT, 'v=DMARC1');
$checks['dmarc'] = [
    'name' => 'DMARC Record',
    'status' => $dmarc_check['found'],
    'value' => $dmarc_check['value'],
    'description' => 'Tells email providers how to handle failed authentication',
    'fix' => 'Add TXT record: v=DMARC1; p=quarantine; rua=mailto:<EMAIL>'
];

// MX Check
$mx_check = check_dns_record($domain, DNS_MX);
$checks['mx'] = [
    'name' => 'MX Record',
    'status' => $mx_check['found'],
    'value' => $mx_check['value'],
    'description' => 'Specifies mail servers for your domain',
    'fix' => 'Contact hosting provider to set up MX records'
];

// Calculate overall score
$total_checks = count($checks);
$passed_checks = array_sum(array_column($checks, 'status'));
$score = round(($passed_checks / $total_checks) * 100);

?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-12 col-lg-10">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="bi bi-shield-check"></i> Email Deliverability Checker</h2>
                <a href="bulk_email.php" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> Back to Bulk Email
                </a>
            </div>

            <!-- Overall Score -->
            <div class="card shadow-sm mb-4">
                <div class="card-body text-center">
                    <h3>Deliverability Score</h3>
                    <div class="display-4 <?php echo $score >= 75 ? 'text-success' : ($score >= 50 ? 'text-warning' : 'text-danger'); ?>">
                        <?php echo $score; ?>%
                    </div>
                    <p class="text-muted">
                        <?php if ($score >= 75): ?>
                            <i class="bi bi-check-circle text-success"></i> Good email deliverability
                        <?php elseif ($score >= 50): ?>
                            <i class="bi bi-exclamation-triangle text-warning"></i> Moderate deliverability - needs improvement
                        <?php else: ?>
                            <i class="bi bi-x-circle text-danger"></i> Poor deliverability - emails likely going to spam
                        <?php endif; ?>
                    </p>
                </div>
            </div>

            <!-- DNS Checks -->
            <div class="row">
                <?php foreach ($checks as $key => $check): ?>
                <div class="col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><?php echo $check['name']; ?></h5>
                            <?php if ($check['status']): ?>
                                <span class="badge bg-success"><i class="bi bi-check-circle"></i> Found</span>
                            <?php else: ?>
                                <span class="badge bg-danger"><i class="bi bi-x-circle"></i> Missing</span>
                            <?php endif; ?>
                        </div>
                        <div class="card-body">
                            <p class="text-muted"><?php echo $check['description']; ?></p>
                            
                            <?php if ($check['status'] && $check['value']): ?>
                                <div class="alert alert-success">
                                    <strong>Current Value:</strong><br>
                                    <code style="word-break: break-all;"><?php echo htmlspecialchars($check['value']); ?></code>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-warning">
                                    <strong>Action Required:</strong><br>
                                    <?php echo $check['fix']; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-lightning"></i> Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Immediate Actions:</h6>
                            <ul class="list-unstyled">
                                <li><i class="bi bi-1-circle text-primary"></i> Add SPF record to DNS</li>
                                <li><i class="bi bi-2-circle text-primary"></i> Contact hosting provider for DKIM</li>
                                <li><i class="bi bi-3-circle text-primary"></i> Set up Gmail SMTP</li>
                                <li><i class="bi bi-4-circle text-primary"></i> Test with mail-tester.com</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>Useful Tools:</h6>
                            <ul class="list-unstyled">
                                <li><a href="https://www.mail-tester.com/" target="_blank" class="text-decoration-none">
                                    <i class="bi bi-box-arrow-up-right"></i> Mail Tester (Spam Score)
                                </a></li>
                                <li><a href="https://mxtoolbox.com/spf.aspx" target="_blank" class="text-decoration-none">
                                    <i class="bi bi-box-arrow-up-right"></i> SPF Record Checker
                                </a></li>
                                <li><a href="https://dnschecker.org/" target="_blank" class="text-decoration-none">
                                    <i class="bi bi-box-arrow-up-right"></i> DNS Propagation Checker
                                </a></li>
                                <li><a href="https://postmaster.google.com/" target="_blank" class="text-decoration-none">
                                    <i class="bi bi-box-arrow-up-right"></i> Gmail Postmaster Tools
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Refresh Button -->
            <div class="text-center mt-4">
                <a href="<?php echo $_SERVER['PHP_SELF']; ?>" class="btn btn-primary">
                    <i class="bi bi-arrow-clockwise"></i> Refresh Check
                </a>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
