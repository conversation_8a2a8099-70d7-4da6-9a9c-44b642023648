<?php
session_start();
require 'includes/db.php';
require 'includes/email.php';

$error = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = $_POST['name'];
    $email = $_POST['email'];
    $password = $_POST['password'];
    $phone_number = $_POST['phone'];
    
    $stmt = $conn->prepare('SELECT id FROM users WHERE email = ?');
    $stmt->bind_param('s', $email);
    $stmt->execute();
    $stmt->store_result();
    if ($stmt->num_rows > 0) {
        $error = 'Email already exists';
    } else {
        $hash = password_hash($password, PASSWORD_DEFAULT);
        $stmt = $conn->prepare('INSERT INTO users (full_name, email, password, phone_number) VALUES (?, ?, ?, ?)');
        $stmt->bind_param('ssss', $name, $email, $hash, $phone_number);
        $stmt->execute();
        $_SESSION['user'] = [
            'id' => $conn->insert_id,
            'full_name' => $name,
            'email' => $email,
            'account_type' => 'Customer'
        ];
        // Send welcome email
        if (!empty($email)) {
            $subject = 'Welcome to ElsBee Data - Your Account is Ready';
            $msg = '
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>Welcome to ElsBee Data</title>
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                    .header { background: #6a6ee7; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
                    .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
                    .button { display: inline-block; background: #6a6ee7; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
                    .footer { text-align: center; margin-top: 20px; color: #666; font-size: 12px; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h2>🎉 Welcome to ElsBee Data!</h2>
                    </div>
                    <div class="content">
                        <p>Dear ' . htmlspecialchars($name) . ',</p>
                        <p>Thank you for joining ElsBee Data! We\'re excited to have you as part of our community.</p>
                        <p>With your new account, you can:</p>
                        <ul>
                            <li>Purchase data bundles at the best prices</li>
                            <li>Track your orders in real-time</li>
                            <li>Get instant delivery notifications</li>
                            <li>Access exclusive deals and promotions</li>
                        </ul>
                        <p style="text-align: center;">
                            <a href="https://elsbeedata.site" class="button">Start Shopping Now</a>
                        </p>
                        <p>If you have any questions or need assistance, our support team is here to help!</p>
                        <p>Best regards,<br>The ElsBee Data Team</p>
                    </div>
                    <div class="footer">
                        <p>ElsBee Data - Your trusted data bundle provider in Ghana</p>
                        <p>This is an automated message, please do not reply directly to this email.</p>
                        <p>For support, contact: <EMAIL></p>
                    </div>
                </div>
            </body>
            </html>';

            // form header
            $from_header = 'ElsBee Data <<EMAIL>>';
            
            // Additional headers for better deliverability
            $headers = [
                'MIME-Version: 1.0',
                'Content-Type: text/html; charset=UTF-8',
                'From: ' . $from_header,
                'Reply-To: <EMAIL>',
                'X-Mailer: PHP/' . phpversion(),
                'X-Priority: 1',
                'List-Unsubscribe: <mailto:<EMAIL>>',
                'Precedence: bulk',
                'Message-ID: <' . time() . '.' . md5($email) . '@elsbeedata.site>'
            ];

            // Send email with additional headers
            send_email($email, $subject, $msg, $from_header, $headers);
        }
        header('Location: index.php');
        exit;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Register - ElsBee Data</title>
  <link href="assets/css/style.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body class="login-register-bg">
  <div class="container">
    <div class="row justify-content-center">
      <div class="col-md-6 col-lg-5">
        <div class="auth-card">
          <h3><i class="bi bi-person-plus me-2"></i>Create Account</h3>
          <?php if($error): ?>
            <div class="alert alert-danger">
              <i class="bi bi-exclamation-circle me-2"></i><?php echo $error; ?>
            </div>
          <?php endif; ?>
          <form method="post">
            <div class="form-floating mb-3">
              <input type="text" name="name" class="form-control" id="name" placeholder="John Doe" required>
              <label for="name"><i class="bi bi-person me-2"></i>Full Name</label>
            </div>
            <div class="form-floating mb-3">
              <input type="email" name="email" class="form-control" id="email" placeholder="<EMAIL>" required>
              <label for="email"><i class="bi bi-envelope me-2"></i>Email address</label>
            </div>
            <div class="form-floating mb-4">
              <input type="password" name="password" class="form-control" id="password" placeholder="Password" required>
              <label for="password"><i class="bi bi-lock me-2"></i>Password</label>
            </div>
            <div class="form-floating mb-3">
              <input type="tel" name="phone" class="form-control" id="phone" placeholder="e.g., **********" required>
              <label for="phone"><i class="bi bi-phone me-2"></i>Phone Number</label>
            </div>
            <button type="submit" class="btn w-100">
              <i class="bi bi-person-plus me-2"></i>Create Account
            </button>
          </form>
          <div class="auth-links">
            <p class="mb-0">Already have an account? <a href="login.php">Sign In</a></p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 