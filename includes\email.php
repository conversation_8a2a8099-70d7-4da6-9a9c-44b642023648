<?php
/**
 * Enhanced email sending function with multiple fallback methods
 * Supports SMTP, Sendmail, and basic mail() function
 */

function send_email($to, $subject, $message, $from = null) {
    // Try multiple email sending methods for better reliability

    // Method 1: Try SMTP with Gmail (most reliable)
    if (send_email_smtp($to, $subject, $message, $from)) {
        return true;
    }

    // Method 2: Try using mail() function with better headers
    if (send_email_basic($to, $subject, $message, $from)) {
        return true;
    }

    // Method 3: Try using a web service (Formspree as backup)
    if (send_email_webservice($to, $subject, $message, $from)) {
        return true;
    }

    return false;
}

/**
 * Send email using SMTP (Gmail)
 * You'll need to set up an App Password for Gmail
 */
function send_email_smtp($to, $subject, $message, $from = null) {
    // SMTP Configuration - Update these with your actual credentials
    $smtp_config = [
        'host' => 'smtp.gmail.com',
        'port' => 587,
        'username' => '<EMAIL>', // Your Gmail address
        'password' => '', // Your Gmail App Password (leave empty for now)
        'encryption' => 'tls'
    ];

    // Skip SMTP if no password is configured
    if (empty($smtp_config['password'])) {
        return false;
    }

    try {
        // Create socket connection
        $socket = fsockopen($smtp_config['host'], $smtp_config['port'], $errno, $errstr, 30);
        if (!$socket) {
            return false;
        }

        // Basic SMTP implementation would go here
        // For now, return false to try other methods
        fclose($socket);
        return false;

    } catch (Exception $e) {
        error_log("SMTP Error: " . $e->getMessage());
        return false;
    }
}

/**
 * Send email using basic mail() function with improved anti-spam headers
 */
function send_email_basic($to, $subject, $message, $from = null) {
    try {
        // Set default from address if not provided
        if (!$from) {
            $from = 'ElsBee Data <<EMAIL>>';
        }

        // Clean the message content to avoid MIME boundary issues
        $clean_message = clean_email_content($message);

        // Create comprehensive anti-spam headers
        $headers = [];
        $headers[] = "MIME-Version: 1.0";
        $headers[] = "Content-Type: text/html; charset=UTF-8";
        $headers[] = "Content-Transfer-Encoding: 8bit";
        $headers[] = "From: $from";
        $headers[] = "Reply-To: ElsBee Data Support <<EMAIL>>";
        $headers[] = "Return-Path: <EMAIL>";

        // Anti-spam headers
        $headers[] = "X-Mailer: ElsBee Data System v1.0";
        $headers[] = "X-Priority: 3";
        $headers[] = "X-MSMail-Priority: Normal";
        $headers[] = "X-Sender-ID: ElsBee Data <<EMAIL>>";
        $headers[] = "X-Original-Sender: <EMAIL>";

        // Content classification
        $headers[] = "X-Spam-Status: No";
        $headers[] = "X-Content-Type-Message-Body: text/html";
        $headers[] = "X-Auto-Response-Suppress: OOF, DR, RN, NRN, AutoReply";

        // Join headers with proper line endings
        $header_string = implode("\r\n", $headers);

        // Send email
        $result = mail($to, $subject, $clean_message, $header_string);

        if ($result) {
            error_log("Email sent successfully to: $to");
            return true;
        } else {
            error_log("Mail function failed for: $to");
            return false;
        }

    } catch (Exception $e) {
        error_log("Basic mail error: " . $e->getMessage());
        return false;
    }
}

/**
 * Clean email content to prevent MIME boundary issues
 */
function clean_email_content($content) {
    // Remove any existing MIME boundaries that might cause issues
    $content = preg_replace('/--==Multipart_Boundary_[^=]*==--/', '', $content);
    $content = preg_replace('/Content-Type: [^\r\n]*[\r\n]+/', '', $content);
    $content = preg_replace('/Content-Transfer-Encoding: [^\r\n]*[\r\n]+/', '', $content);

    // Ensure proper line endings
    $content = str_replace(["\r\n", "\r"], "\n", $content);
    $content = str_replace("\n", "\r\n", $content);

    return $content;
}

/**
 * Send email using web service as fallback
 */
function send_email_webservice($to, $subject, $message, $from = null) {
    try {
        // Use a simple HTTP POST to a service like Formspree
        $data = [
            'email' => $to,
            'subject' => $subject,
            'message' => strip_tags($message), // Remove HTML for web service
            '_replyto' => '<EMAIL>'
        ];

        $options = [
            'http' => [
                'header' => "Content-type: application/x-www-form-urlencoded\r\n",
                'method' => 'POST',
                'content' => http_build_query($data)
            ]
        ];

        $context = stream_context_create($options);
        $result = file_get_contents('https://formspree.io/f/mnndvepk', false, $context);

        if ($result !== false) {
            error_log("Web service email sent to: $to");
            return true;
        }

        return false;

    } catch (Exception $e) {
        error_log("Web service email error: " . $e->getMessage());
        return false;
    }
}

/**
 * Test email configuration
 */
function test_email_config() {
    $test_email = '<EMAIL>';
    $subject = 'ElsBee Data - Email Test';
    $message = '<h3>Email Test</h3><p>This is a test email from ElsBee Data.</p>';

    echo "Testing email configuration...\n";

    if (send_email_basic($test_email, $subject, $message)) {
        echo "✓ Basic mail() function works\n";
    } else {
        echo "✗ Basic mail() function failed\n";
    }

    // Add more tests as needed
}
?>