<?php
/**
 * Email Debug Test Script
 * This script helps debug email formatting issues
 */

require_once '../vendor/autoload.php';
require_once '../includes/config.php';

use <PERSON><PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\Exception;

// Include the bulk email functions
require_once 'bulk_email.php';

// Function to display email content for debugging
function debug_email_content($email_address) {
    echo "<h2>Email Debug Test</h2>";
    echo "<p>Testing email formatting for: <strong>" . htmlspecialchars($email_address) . "</strong></p>";
    
    // Get message template
    $msg = get_message_template('Debug User');
    $unsubscribe_token = 'debug_token_123';
    $subject = 'Debug Test Email - ElsBee Data';
    
    // Generate HTML content
    $html_content = get_email_html_template($msg['html'], $subject, $email_address, $unsubscribe_token);
    
    echo "<h3>HTML Content Preview:</h3>";
    echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0; background: #f9f9f9;'>";
    echo "<iframe srcdoc='" . htmlspecialchars($html_content) . "' style='width: 100%; height: 400px; border: none;'></iframe>";
    echo "</div>";
    
    echo "<h3>Plain Text Content:</h3>";
    echo "<pre style='border: 1px solid #ccc; padding: 10px; margin: 10px 0; background: #f9f9f9; white-space: pre-wrap;'>";
    echo htmlspecialchars($msg['plain']);
    echo "</pre>";
    
    echo "<h3>Raw HTML Source:</h3>";
    echo "<textarea style='width: 100%; height: 200px; font-family: monospace; font-size: 12px;'>";
    echo htmlspecialchars($html_content);
    echo "</textarea>";
    
    return $html_content;
}

// Function to send actual test email
function send_debug_email($email_address) {
    $mail = new PHPMailer(true);
    
    try {
        // Clear any previous settings
        $mail->clearAllRecipients();
        $mail->clearAttachments();
        $mail->clearCustomHeaders();
        
        // Server settings
        $mail->isSMTP();
        $mail->Host = 'localhost';
        $mail->SMTPAuth = false;
        $mail->Port = 25;
        $mail->CharSet = 'UTF-8';
        $mail->Encoding = '8bit';
        
        // Enable debug output for testing
        $mail->SMTPDebug = 2;
        $mail->Debugoutput = function($str, $level) {
            echo "<div style='background: #f0f0f0; padding: 5px; margin: 2px 0; font-family: monospace; font-size: 12px;'>";
            echo "DEBUG ($level): " . htmlspecialchars($str);
            echo "</div>";
        };
        
        // Recipients
        $mail->setFrom('<EMAIL>', 'ElsBee Data Debug');
        $mail->addAddress($email_address, 'Debug User');
        $mail->addReplyTo('<EMAIL>', 'ElsBee Data Support');
        
        // Content
        $mail->isHTML(true);
        $mail->Subject = 'Debug Test Email - ElsBee Data';
        
        $msg = get_message_template('Debug User');
        $html_body = get_email_html_template($msg['html'], 'Debug Test Email', $email_address, 'debug_token');
        $mail->Body = $html_body;
        $mail->AltBody = $msg['plain'];
        
        echo "<h3>Sending Email...</h3>";
        $result = $mail->send();
        
        if ($result) {
            echo "<div style='color: green; font-weight: bold;'>✓ Email sent successfully!</div>";
        } else {
            echo "<div style='color: red; font-weight: bold;'>✗ Email failed to send</div>";
        }
        
        return $result;
        
    } catch (Exception $e) {
        echo "<div style='color: red; font-weight: bold;'>✗ Email Error: " . $mail->ErrorInfo . "</div>";
        echo "<div style='color: red;'>Exception: " . $e->getMessage() . "</div>";
        return false;
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Debug Test - ElsBee Data</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1000px; margin: 0 auto; }
        .form-group { margin: 15px 0; }
        input[type="email"] { padding: 8px; width: 300px; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; cursor: pointer; }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Email Debug Test - ElsBee Data</h1>
        
        <form method="post">
            <div class="form-group">
                <label for="email">Test Email Address:</label><br>
                <input type="email" id="email" name="email" value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>" required>
                <button type="submit" name="action" value="preview">Preview Email</button>
                <button type="submit" name="action" value="send">Send Test Email</button>
            </div>
        </form>
        
        <?php
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['email'])) {
            $email = trim($_POST['email']);
            $action = $_POST['action'] ?? 'preview';
            
            if (filter_var($email, FILTER_VALIDATE_EMAIL)) {
                if ($action === 'preview') {
                    debug_email_content($email);
                } elseif ($action === 'send') {
                    debug_email_content($email);
                    echo "<hr>";
                    send_debug_email($email);
                }
            } else {
                echo "<div style='color: red;'>Please enter a valid email address.</div>";
            }
        }
        ?>
        
        <hr>
        <p><a href="bulk_email.php">← Back to Bulk Email Sender</a></p>
    </div>
</body>
</html>
