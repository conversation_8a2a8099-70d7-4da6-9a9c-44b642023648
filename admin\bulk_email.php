<?php
session_start();

// Determine the base path of the application
$base_path = '/datahub/'; 

require '../includes/email.php';
require '../includes/db.php';
require '../includes/auth.php';
require '../vendor/autoload.php'; // PHPMailer autoload

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

// require_login() will redirect to login page if not logged in
require_login($base_path);

if ($_SESSION['user']['account_type'] !== 'Admin') {
    header('Location: ' . $base_path . 'index.php');
    exit;
}

$success = $error = '';

// Improved message template with anti-spam content
function get_message_template($name = 'Customer') {
    $html = '<div style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
<p>Hello ' . htmlspecialchars($name) . ',</p>

<p>Thank you for being a valued customer of ElsBee Data. We would like to invite you to join our official communication channels for better service and updates.</p>

<h3 style="color: #6a6ee7;">Stay Connected with ElsBee Data</h3>

<div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;">
<p><strong>� Official WhatsApp Channel</strong><br>
Get instant notifications about data bundle offers, service updates, and important announcements.<br>
<a href="https://whatsapp.com/channel/0029Vb6mfe1J93wXfJpdZR26"><span style="background-color: #6a6ee7; color: white; padding: 5px; border-radius: 5px; text-decoration: none;"> Join our WhatsApp Channel</span></a></p>
</div>

<div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;">
<p><strong>💬 Community Discussion Group</strong><br>
Connect with other customers, share experiences, and get support from our community.<br>
<a href="https://chat.whatsapp.com/CaiJeS1VgKJJBDz1dHWDVT"><span style="background-color: #6a6ee7; color: white; padding: 5px; border-radius: 5px; text-decoration: none;"> Join our Community Group</span></a></p>
</div>

<p><strong>Benefits of joining:</strong></p>
<ul style="padding-left: 20px;">
<li>Receive exclusive offers and promotions</li>
<li>Get priority customer support</li>
<li>Stay informed about new services</li>
<li>Connect with other ElsBee Data customers</li>
</ul>

<p>These channels are managed by our official team and provide a secure way to stay updated with our services.</p>

<p>Best regards,<br>
<strong>Elvis Brown</strong><br>
Customer Relations Manager<br>
ElsBee Data Ghana</p>
</div>';

    $plain = "Hello $name,\n\n" .
        "Thank you for being a valued customer of ElsBee Data. We would like to invite you to join our official communication channels for better service and updates.\n\n" .
        "STAY CONNECTED WITH ELSBEE DATA\n\n" .
        "Official WhatsApp Channel:\n" .
        "Get instant notifications about data bundle offers, service updates, and important announcements.\n" .
        "Join here: https://whatsapp.com/channel/0029Vb6mfe1J93wXfJpdZR26\n\n" .
        "Community Discussion Group:\n" .
        "Connect with other customers, share experiences, and get support from our community.\n" .
        "Join here: https://chat.whatsapp.com/CaiJeS1VgKJJBDz1dHWDVT\n\n" .
        "Benefits of joining:\n" .
        "- Receive exclusive offers and promotions\n" .
        "- Get priority customer support\n" .
        "- Stay informed about new services\n" .
        "- Connect with other ElsBee Data customers\n\n" .
        "These channels are managed by our official team and provide a secure way to stay updated with our services.\n\n" .
        "Best regards,\n" .
        "Elvis Brown\n" .
        "Customer Relations Manager\n" .
        "ElsBee Data Ghana";

    return ['html' => $html, 'plain' => $plain];
}

// Create clean HTML email template
function get_email_html_template($content, $subject, $email, $unsubscribe_token) {
    $html = '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>' . htmlspecialchars($subject) . '</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
        }
        .header {
            background: #6a6ee7;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background: #fff;
            padding: 30px;
            border-radius: 0 0 8px 8px;
            border: 1px solid #e0e0e0;
            border-top: none;
        }
        .footer {
            text-align: center;
            margin-top: 20px;
            color: #666;
            font-size: 12px;
            padding: 20px;
            background: #f9f9f9;
            border-radius: 8px;
        }
        .unsubscribe {
            color: #666;
            text-decoration: none;
            font-size: 12px;
        }
        @media only screen and (max-width: 600px) {
            .container {
                width: 100% !important;
                padding: 10px !important;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>ElsBee Data</h2>
        </div>
        <div class="content">
            ' . $content . '
        </div>
        <div class="footer">
            <p>ElsBee Data - Your trusted data bundle provider in Ghana</p>
            <p>This is an automated message, please do not reply directly to this email.</p>
            <p>For support, contact: <a href="mailto:<EMAIL>"><EMAIL></a></p>
            <p><a href="https://elsbeedata.site/unsubscribe?email=' . urlencode($email) . '&token=' . $unsubscribe_token . '" class="unsubscribe">Unsubscribe from these emails</a></p>
        </div>
    </div>
</body>
</html>';

    return $html;
}

// Add anti-spam headers to improve deliverability
function add_anti_spam_headers($mail, $email, $unsubscribe_token) {
    // List management headers (required for bulk email)
    $mail->addCustomHeader('List-Unsubscribe', '<mailto:<EMAIL>?subject=Unsubscribe>, <https://elsbeedata.site/unsubscribe?email=' . urlencode($email) . '&token=' . $unsubscribe_token . '>');
    $mail->addCustomHeader('List-Unsubscribe-Post', 'List-Unsubscribe=One-Click');
    $mail->addCustomHeader('List-ID', 'ElsBee Data Newsletter <newsletter.elsbeedata.site>');

    // Sender identification headers
    $mail->addCustomHeader('X-Mailer', 'ElsBee Data Newsletter System v1.0');
    $mail->addCustomHeader('X-Auto-Response-Suppress', 'OOF, DR, RN, NRN, AutoReply');
    $mail->addCustomHeader('X-Priority', '3');
    $mail->addCustomHeader('Precedence', 'bulk');

    // Authentication and reputation headers
    $mail->addCustomHeader('X-Sender-ID', 'ElsBee Data <<EMAIL>>');
    $mail->addCustomHeader('X-Original-Sender', '<EMAIL>');
    $mail->addCustomHeader('Return-Path', '<EMAIL>');

    // Content classification headers
    $mail->addCustomHeader('X-MS-Exchange-Organization-SCL', '-1');
    $mail->addCustomHeader('X-Spam-Status', 'No');
    $mail->addCustomHeader('X-Content-Type-Message-Body', 'text/html');

    // Feedback loop headers
    $mail->addCustomHeader('X-Feedback-ID', 'newsletter:elsbeedata:site');
    $mail->addCustomHeader('X-Campaign-ID', 'whatsapp-community-invite-' . date('Y-m'));

    // Message classification
    $mail->addCustomHeader('X-Message-Type', 'newsletter');
    $mail->addCustomHeader('X-Bulk-ID', 'elsbeedata-newsletter');
}

// Test email function
function send_test_email($test_email) {
    $mail = new PHPMailer(true);
    try {
        // Clear any previous settings
        $mail->clearAllRecipients();
        $mail->clearAttachments();
        $mail->clearCustomHeaders();

        //Server settings
        $mail->isSMTP();
        $mail->Host       = 'localhost';
        $mail->SMTPAuth   = false;
        $mail->Port       = 25;
        $mail->CharSet    = 'UTF-8';
        $mail->Encoding   = '8bit';

        // Disable SMTP debug output
        $mail->SMTPDebug = 0;
        $mail->Debugoutput = 'error_log';

        //Recipients
        $mail->setFrom('<EMAIL>', 'ElsBee Data');
        $mail->addAddress($test_email, 'Test User');
        $mail->addReplyTo('<EMAIL>', 'ElsBee Data Support');

        //Content
        $mail->isHTML(true);
        $mail->Subject = 'Test Email - ElsBee Data';

        $msg = get_message_template('Test User');
        $html_body = get_email_html_template($msg['html'], 'Test Email', $test_email, 'test_token');
        $mail->Body = $html_body;
        $mail->AltBody = $msg['plain'];

        $mail->send();
        return true;
    } catch (Exception $e) {
        error_log('Test Email Error: ' . $mail->ErrorInfo);
        return false;
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['test_email'])) {
        $test_email = trim($_POST['test_email']);
        if (filter_var($test_email, FILTER_VALIDATE_EMAIL)) {
            if (send_test_email($test_email)) {
                $success = "Test email sent successfully to: $test_email";
            } else {
                $error = "Failed to send test email to: $test_email";
            }
        } else {
            $error = "Please enter a valid email address for testing.";
        }
    } elseif (isset($_POST['emails'])) {
        $emails_raw = array_filter(array_map('trim', explode("\n", $_POST['emails'])));
        $subject = isset($_POST['subject']) && trim($_POST['subject']) ? trim($_POST['subject']) : 'Stay Connected with ElsBee Data - Official Communication Channels';
        // Parse emails and names
        $recipients = [];
        foreach ($emails_raw as $line) {
            if (preg_match('/^(.+?)\s*<(.+@.+)>$/', $line, $matches)) {
                $name = trim($matches[1]);
                $email = trim($matches[2]);
            } elseif (filter_var($line, FILTER_VALIDATE_EMAIL)) {
                $name = 'Customer';
                $email = $line;
            } else {
                continue;
            }
            $recipients[] = ['name' => $name, 'email' => $email];
        }
        if (empty($recipients)) {
            $error = 'Please enter at least one valid email address.';
        } else {
            $success_count = 0;
            $failed_count = 0;
            $from_email = '<EMAIL>';
            $from_name = 'ElsBee Data';
            $delay = 5; // Increased delay to avoid spam filters
            foreach ($recipients as $recipient) {
                $name = $recipient['name'];
                $email = $recipient['email'];
                $unsubscribe_token = hash('sha256', $email . time() . uniqid());
                $msg = get_message_template($name);
                $mail = new PHPMailer(true);
                try {
                    // Clear any previous settings
                    $mail->clearAllRecipients();
                    $mail->clearAttachments();
                    $mail->clearCustomHeaders();

                    //Server settings
                    $mail->isSMTP();
                    $mail->Host       = 'localhost'; // cPanel default
                    $mail->SMTPAuth   = false;
                    $mail->Port       = 25;
                    $mail->CharSet    = 'UTF-8';
                    $mail->Encoding   = '8bit';

                    // Disable SMTP debug output
                    $mail->SMTPDebug = 0;
                    $mail->Debugoutput = 'error_log';

                    //Recipients
                    $mail->setFrom($from_email, $from_name);
                    $mail->addAddress($email, $name);
                    $mail->addReplyTo('<EMAIL>', 'ElsBee Data Support');

                    //Content - Set HTML mode first
                    $mail->isHTML(true);
                    $mail->Subject = $subject;

                    // Create clean HTML body
                    $html_body = get_email_html_template($msg['html'], $subject, $email, $unsubscribe_token);
                    $mail->Body = $html_body;
                    $mail->AltBody = $msg['plain'];

                    // Add anti-spam headers
                    add_anti_spam_headers($mail, $email, $unsubscribe_token);

                    $mail->send();
                    $success_count++;
                } catch (Exception $e) {
                    error_log('Mailer Error: ' . $mail->ErrorInfo);
                    $failed_count++;
                }
                sleep($delay);
            }
            if ($success_count > 0) {
                $success = "Successfully sent {$success_count} email(s).";
            }
            if ($failed_count > 0) {
                $error = "Failed to send {$failed_count} email(s).";
            }
        }
    }
}

include '../includes/header.php';
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-12 col-lg-8">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="bi bi-envelope"></i> Bulk Email Sender</h2>
                <div>
                    <a href="email_deliverability_checker.php" class="btn btn-warning me-2">
                        <i class="bi bi-shield-check"></i> Check Deliverability
                    </a>
                    <a href="<?php echo $base_path; ?>admin/dashboard.php" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i> Back to Dashboard
                    </a>
                </div>
            </div>

            <?php if ($success): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle"></i> <?php echo $success; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle"></i> <?php echo $error; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Spam Warning -->
            <div class="alert alert-warning alert-dismissible fade show" role="alert">
                <h5><i class="bi bi-exclamation-triangle"></i> Emails Going to Spam?</h5>
                <p class="mb-2">If your emails are going to spam folders, you need to set up email authentication (SPF, DKIM, DMARC) for your domain.</p>
                <div class="d-flex gap-2">
                    <a href="email_deliverability_checker.php" class="btn btn-sm btn-outline-warning">
                        <i class="bi bi-shield-check"></i> Check Email Setup
                    </a>
                    <a href="../EMAIL_DELIVERABILITY_GUIDE.md" target="_blank" class="btn btn-sm btn-outline-info">
                        <i class="bi bi-book"></i> Setup Guide
                    </a>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>

            <!-- Test Email Card -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="bi bi-envelope-check"></i> Test Email</h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">Send a test email to verify formatting before sending bulk emails.</p>
                    <form method="post" class="row g-3">
                        <div class="col-md-8">
                            <input type="email" class="form-control" name="test_email" placeholder="Enter your email address" required>
                        </div>
                        <div class="col-md-4">
                            <button type="submit" class="btn btn-info w-100">
                                <i class="bi bi-send-check"></i> Send Test
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Bulk Email Card -->
            <div class="card shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-envelope-plus"></i> Bulk Email Sender</h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        <div class="mb-3">
                            <label for="emails" class="form-label">Names & Email Addresses</label>
                            <textarea class="form-control" id="emails" name="emails" rows="5" placeholder="e.g. John Doe <<EMAIL>>\nJane <<EMAIL>>\<EMAIL>" required><?php echo isset($_POST['emails']) ? htmlspecialchars($_POST['emails']) : ''; ?></textarea>
                            <div class="form-text">Enter one per line. Format: <code>Name &lt;email&gt;</code> or just <code>email</code>.</div>
                        </div>
                        <div class="mb-3">
                            <label for="subject" class="form-label">Subject (optional)</label>
                            <input type="text" class="form-control" id="subject" name="subject" value="<?php echo isset($_POST['subject']) ? htmlspecialchars($_POST['subject']) : ''; ?>" placeholder="Stay Connected with ElsBee Data - Official Communication Channels">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Message Preview</label>
                            <div class="border rounded p-3 bg-light" style="white-space: pre-line; font-family: inherit;">
                                <?php $preview = get_message_template('John Doe'); echo strip_tags($preview['plain']); ?>
                            </div>
                            <div class="form-text">This message will be sent to all recipients, personalized with their name.</div>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-send"></i> Send Emails
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?> 