<?php
session_start();

// Determine the base path of the application
$base_path = '/datahub/'; 

require '../includes/email.php';
require '../includes/db.php';
require '../includes/auth.php';
require '../vendor/autoload.php'; // PHPMailer autoload

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

// require_login() will redirect to login page if not logged in
require_login($base_path);

if ($_SESSION['user']['account_type'] !== 'Admin') {
    header('Location: ' . $base_path . 'index.php');
    exit;
}

$success = $error = '';

// Fixed message template
function get_message_template($name = 'Customer') {
    $html = '<p>Dear ' . htmlspecialchars($name) . ',</p>
<p>We\'re excited to invite you to join our growing WhatsApp Community and Channel! This is your gateway to exclusive updates, valuable insights, and real-time interactions with like-minded individuals.</p>
<ul>
<li>🔹 <b>WhatsApp Channel:</b> Stay updated with curated content, announcements, and resources. Join here:<br>👉 <a href="https://whatsapp.com/channel/0029Vb6mfe1J93wXfJpdZR26">https://whatsapp.com/channel/0029Vb6mfe1J93wXfJpdZR26</a></li>
<li>🔹 <b>WhatsApp Community:</b> Engage in discussions, share ideas, and connect with others. Join here:<br>👉 <a href="https://chat.whatsapp.com/CaiJeS1VgKJJBDz1dHWDVT">https://chat.whatsapp.com/CaiJeS1VgKJJBDz1dHWDVT</a></li>
</ul>
<p>By joining, you\'ll be the first to access new features, tips, and opportunities from ElsbeeData. Don\'t miss out—click the links above to get started!</p>
<p>Looking forward to connecting with you there.</p>
<p>Best regards,<br>Elvis Brown<br>Admin<br>ElsbeeData</p>';

    $plain = "Dear $name,\n\n" .
        "We're excited to invite you to join our growing WhatsApp Community and Channel! This is your gateway to exclusive updates, valuable insights, and real-time interactions with like-minded individuals.\n\n" .
        "WhatsApp Channel: Stay updated with curated content, announcements, and resources. Join here:\nhttps://whatsapp.com/channel/0029Vb6mfe1J93wXfJpdZR26\n\n" .
        "WhatsApp Community: Engage in discussions, share ideas, and connect with others. Join here:\nhttps://chat.whatsapp.com/CaiJeS1VgKJJBDz1dHWDVT\n\n" .
        "By joining, you'll be the first to access new features, tips, and opportunities from ElsbeeData. Don't miss out—click the links above to get started!\n\n" .
        "Looking forward to connecting with you there.\n\n" .
        "Best regards,\nElvis Brown\nAdmin\nElsbeeData";

    return ['html' => $html, 'plain' => $plain];
}

// Create clean HTML email template
function get_email_html_template($content, $subject, $email, $unsubscribe_token) {
    $html = '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>' . htmlspecialchars($subject) . '</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
        }
        .header {
            background: #6a6ee7;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background: #fff;
            padding: 30px;
            border-radius: 0 0 8px 8px;
            border: 1px solid #e0e0e0;
            border-top: none;
        }
        .footer {
            text-align: center;
            margin-top: 20px;
            color: #666;
            font-size: 12px;
            padding: 20px;
            background: #f9f9f9;
            border-radius: 8px;
        }
        .unsubscribe {
            color: #666;
            text-decoration: none;
            font-size: 12px;
        }
        @media only screen and (max-width: 600px) {
            .container {
                width: 100% !important;
                padding: 10px !important;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>ElsBee Data</h2>
        </div>
        <div class="content">
            ' . $content . '
        </div>
        <div class="footer">
            <p>ElsBee Data - Your trusted data bundle provider in Ghana</p>
            <p>This is an automated message, please do not reply directly to this email.</p>
            <p>For support, contact: <a href="mailto:<EMAIL>"><EMAIL></a></p>
            <p><a href="https://elsbeedata.site/unsubscribe?email=' . urlencode($email) . '&token=' . $unsubscribe_token . '" class="unsubscribe">Unsubscribe from these emails</a></p>
        </div>
    </div>
</body>
</html>';

    return $html;
}

// Test email function
function send_test_email($test_email) {
    $mail = new PHPMailer(true);
    try {
        // Clear any previous settings
        $mail->clearAllRecipients();
        $mail->clearAttachments();
        $mail->clearCustomHeaders();

        //Server settings
        $mail->isSMTP();
        $mail->Host       = 'localhost';
        $mail->SMTPAuth   = false;
        $mail->Port       = 25;
        $mail->CharSet    = 'UTF-8';
        $mail->Encoding   = '8bit';

        // Disable SMTP debug output
        $mail->SMTPDebug = 0;
        $mail->Debugoutput = 'error_log';

        //Recipients
        $mail->setFrom('<EMAIL>', 'ElsBee Data');
        $mail->addAddress($test_email, 'Test User');
        $mail->addReplyTo('<EMAIL>', 'ElsBee Data Support');

        //Content
        $mail->isHTML(true);
        $mail->Subject = 'Test Email - ElsBee Data';

        $msg = get_message_template('Test User');
        $html_body = get_email_html_template($msg['html'], 'Test Email', $test_email, 'test_token');
        $mail->Body = $html_body;
        $mail->AltBody = $msg['plain'];

        $mail->send();
        return true;
    } catch (Exception $e) {
        error_log('Test Email Error: ' . $mail->ErrorInfo);
        return false;
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['test_email'])) {
        $test_email = trim($_POST['test_email']);
        if (filter_var($test_email, FILTER_VALIDATE_EMAIL)) {
            if (send_test_email($test_email)) {
                $success = "Test email sent successfully to: $test_email";
            } else {
                $error = "Failed to send test email to: $test_email";
            }
        } else {
            $error = "Please enter a valid email address for testing.";
        }
    } elseif (isset($_POST['emails'])) {
        $emails_raw = array_filter(array_map('trim', explode("\n", $_POST['emails'])));
        $subject = isset($_POST['subject']) && trim($_POST['subject']) ? trim($_POST['subject']) : 'Join Our WhatsApp Community & Channel – Exclusive Updates Await!';
        // Parse emails and names
        $recipients = [];
        foreach ($emails_raw as $line) {
            if (preg_match('/^(.+?)\s*<(.+@.+)>$/', $line, $matches)) {
                $name = trim($matches[1]);
                $email = trim($matches[2]);
            } elseif (filter_var($line, FILTER_VALIDATE_EMAIL)) {
                $name = 'Customer';
                $email = $line;
            } else {
                continue;
            }
            $recipients[] = ['name' => $name, 'email' => $email];
        }
        if (empty($recipients)) {
            $error = 'Please enter at least one valid email address.';
        } else {
            $success_count = 0;
            $failed_count = 0;
            $from_email = '<EMAIL>';
            $from_name = 'ElsBee Data';
            $delay = 2;
            foreach ($recipients as $recipient) {
                $name = $recipient['name'];
                $email = $recipient['email'];
                $unsubscribe_token = hash('sha256', $email . time() . uniqid());
                $msg = get_message_template($name);
                $mail = new PHPMailer(true);
                try {
                    // Clear any previous settings
                    $mail->clearAllRecipients();
                    $mail->clearAttachments();
                    $mail->clearCustomHeaders();

                    //Server settings
                    $mail->isSMTP();
                    $mail->Host       = 'localhost'; // cPanel default
                    $mail->SMTPAuth   = false;
                    $mail->Port       = 25;
                    $mail->CharSet    = 'UTF-8';
                    $mail->Encoding   = '8bit';

                    // Disable SMTP debug output
                    $mail->SMTPDebug = 0;
                    $mail->Debugoutput = 'error_log';

                    //Recipients
                    $mail->setFrom($from_email, $from_name);
                    $mail->addAddress($email, $name);
                    $mail->addReplyTo('<EMAIL>', 'ElsBee Data Support');

                    //Content - Set HTML mode first
                    $mail->isHTML(true);
                    $mail->Subject = $subject;

                    // Create clean HTML body
                    $html_body = get_email_html_template($msg['html'], $subject, $email, $unsubscribe_token);
                    $mail->Body = $html_body;
                    $mail->AltBody = $msg['plain'];

                    // Add headers for bulk email
                    $mail->addCustomHeader('List-Unsubscribe', '<mailto:<EMAIL>>');
                    $mail->addCustomHeader('Precedence', 'bulk');
                    $mail->addCustomHeader('X-Priority', '3');
                    $mail->addCustomHeader('X-Mailer', 'ElsBee Data Bulk Sender');

                    $mail->send();
                    $success_count++;
                } catch (Exception $e) {
                    error_log('Mailer Error: ' . $mail->ErrorInfo);
                    $failed_count++;
                }
                sleep($delay);
            }
            if ($success_count > 0) {
                $success = "Successfully sent {$success_count} email(s).";
            }
            if ($failed_count > 0) {
                $error = "Failed to send {$failed_count} email(s).";
            }
        }
    }
}

include '../includes/header.php';
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-12 col-lg-8">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="bi bi-envelope"></i> Bulk Email Sender</h2>
                <a href="<?php echo $base_path; ?>admin/dashboard.php" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> Back to Dashboard
                </a>
            </div>

            <?php if ($success): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle"></i> <?php echo $success; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle"></i> <?php echo $error; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Test Email Card -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="bi bi-envelope-check"></i> Test Email</h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">Send a test email to verify formatting before sending bulk emails.</p>
                    <form method="post" class="row g-3">
                        <div class="col-md-8">
                            <input type="email" class="form-control" name="test_email" placeholder="Enter your email address" required>
                        </div>
                        <div class="col-md-4">
                            <button type="submit" class="btn btn-info w-100">
                                <i class="bi bi-send-check"></i> Send Test
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Bulk Email Card -->
            <div class="card shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-envelope-plus"></i> Bulk Email Sender</h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        <div class="mb-3">
                            <label for="emails" class="form-label">Names & Email Addresses</label>
                            <textarea class="form-control" id="emails" name="emails" rows="5" placeholder="e.g. John Doe <<EMAIL>>\nJane <<EMAIL>>\<EMAIL>" required><?php echo isset($_POST['emails']) ? htmlspecialchars($_POST['emails']) : ''; ?></textarea>
                            <div class="form-text">Enter one per line. Format: <code>Name &lt;email&gt;</code> or just <code>email</code>.</div>
                        </div>
                        <div class="mb-3">
                            <label for="subject" class="form-label">Subject (optional)</label>
                            <input type="text" class="form-control" id="subject" name="subject" value="<?php echo isset($_POST['subject']) ? htmlspecialchars($_POST['subject']) : ''; ?>" placeholder="Join Our WhatsApp Community & Channel – Exclusive Updates Await!">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Message Preview</label>
                            <div class="border rounded p-3 bg-light" style="white-space: pre-line; font-family: inherit;">
                                <?php $preview = get_message_template('John Doe'); echo strip_tags($preview['plain']); ?>
                            </div>
                            <div class="form-text">This message will be sent to all recipients, personalized with their name.</div>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-send"></i> Send Emails
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?> 